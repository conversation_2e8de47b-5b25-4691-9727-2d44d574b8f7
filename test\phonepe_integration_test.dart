import 'package:flutter_test/flutter_test.dart';
import 'package:ecoplug/services/payment/phonepe_service.dart';

void main() {
  group('PhonePe Integration Tests', () {
    test('PaymentResult factory methods should create correct result types', () {
      // Test success result
      final successResult = PaymentResult.success({'transactionId': 'test123'});
      expect(successResult.type, PaymentResultType.success);
      expect(successResult.message, 'Payment completed successfully');
      expect(successResult.data?['transactionId'], 'test123');

      // Test failed result
      final failedResult = PaymentResult.failed('Payment failed');
      expect(failedResult.type, PaymentResultType.failed);
      expect(failedResult.message, 'Payment failed');

      // Test interrupted result (replaces cancelled)
      final interruptedResult = PaymentResult.interrupted();
      expect(interruptedResult.type, PaymentResultType.interrupted);
      expect(interruptedResult.message, 'Payment interrupted by user');
      expect(interruptedResult.isUserCancelled, true);

      // Test timeout result
      final timeoutResult = PaymentResult.timeout();
      expect(timeoutResult.type, PaymentResultType.timeout);
      expect(timeoutResult.message, 'Payment timed out');

      // Test network error result
      final networkErrorResult = PaymentResult.networkError();
      expect(networkErrorResult.type, PaymentResultType.networkError);
      expect(networkErrorResult.message, 'Network error occurred');

      // Test app crash result
      final appCrashResult = PaymentResult.appCrash();
      expect(appCrashResult.type, PaymentResultType.appCrash);
      expect(appCrashResult.message, 'PhonePe app crashed');

      // Test invalid response result
      final invalidResponseResult = PaymentResult.invalidResponse('Invalid data');
      expect(invalidResponseResult.type, PaymentResultType.invalidResponse);
      expect(invalidResponseResult.message, 'Invalid response from payment gateway');

      // Test unknown result
      final unknownResult = PaymentResult.unknown('Unknown error');
      expect(unknownResult.type, PaymentResultType.unknown);
      expect(unknownResult.message, 'Unknown error occurred during payment');
    });

    test('PaymentResultType enum should have all required values', () {
      // Test that all required enum values exist
      expect(PaymentResultType.success, isNotNull);
      expect(PaymentResultType.failed, isNotNull);
      expect(PaymentResultType.interrupted, isNotNull);
      expect(PaymentResultType.timeout, isNotNull);
      expect(PaymentResultType.networkError, isNotNull);
      expect(PaymentResultType.appCrash, isNotNull);
      expect(PaymentResultType.invalidResponse, isNotNull);
      expect(PaymentResultType.unknown, isNotNull);
      expect(PaymentResultType.pending, isNotNull);

      // Verify enum values count (should be 9 total)
      expect(PaymentResultType.values.length, 9);
    });

    test('PhonePe SDK initialization should validate parameters', () async {
      // Test valid parameters
      expect(
        () => PhonePeService.init(
          environment: 'SANDBOX',
          merchantId: 'TEST_MERCHANT',
          flowId: 'TEST_FLOW',
          enableLogging: true,
        ),
        returnsNormally,
      );

      // Test empty environment
      expect(
        () => PhonePeService.init(
          environment: '',
          merchantId: 'TEST_MERCHANT',
          flowId: 'TEST_FLOW',
        ),
        throwsA(isA<ArgumentError>()),
      );

      // Test empty merchant ID
      expect(
        () => PhonePeService.init(
          environment: 'SANDBOX',
          merchantId: '',
          flowId: 'TEST_FLOW',
        ),
        throwsA(isA<ArgumentError>()),
      );

      // Test empty flow ID
      expect(
        () => PhonePeService.init(
          environment: 'SANDBOX',
          merchantId: 'TEST_MERCHANT',
          flowId: '',
        ),
        throwsA(isA<ArgumentError>()),
      );
    });

    test('Payment result should handle error codes correctly', () {
      // Test failed result with error code
      final failedWithCode = PaymentResult.failed(
        'Payment declined',
        errorCode: 'INSUFFICIENT_FUNDS',
        data: {'balance': 0},
      );
      
      expect(failedWithCode.type, PaymentResultType.failed);
      expect(failedWithCode.message, 'Payment declined');
      expect(failedWithCode.errorCode, 'INSUFFICIENT_FUNDS');
      expect(failedWithCode.data?['balance'], 0);
    });

    test('Payment result should handle user cancellation flags correctly', () {
      // Test interrupted result (user cancelled)
      final interruptedResult = PaymentResult.interrupted();
      expect(interruptedResult.isUserCancelled, true);

      // Test failed result (not user cancelled)
      final failedResult = PaymentResult.failed('Technical error');
      expect(failedResult.isUserCancelled, false);

      // Test success result (not user cancelled)
      final successResult = PaymentResult.success({});
      expect(successResult.isUserCancelled, false);
    });

    test('PhonePe service should handle callback registration', () {
      // Test callback registration
      bool callbackCalled = false;
      PaymentResult? receivedResult;
      String? receivedTransactionId;

      PhonePeService.registerServerNotificationCallback((result, transactionId) async {
        callbackCalled = true;
        receivedResult = result;
        receivedTransactionId = transactionId;
      });

      // Verify callback was registered (this is a basic test)
      expect(PhonePeService.hasRegisteredCallback, true);
    });

    test('Payment result data should be properly structured', () {
      final testData = {
        'transactionId': 'TXN123',
        'amount': 100.0,
        'currency': 'INR',
        'timestamp': DateTime.now().toIso8601String(),
      };

      final result = PaymentResult.success(testData);
      
      expect(result.data?['transactionId'], 'TXN123');
      expect(result.data?['amount'], 100.0);
      expect(result.data?['currency'], 'INR');
      expect(result.data?['timestamp'], isNotNull);
    });

    test('Error handling should provide meaningful messages', () {
      // Test various error scenarios
      final networkError = PaymentResult.networkError();
      expect(networkError.message, contains('Network'));

      final timeoutError = PaymentResult.timeout();
      expect(timeoutError.message, contains('timed out'));

      final appCrashError = PaymentResult.appCrash();
      expect(appCrashError.message, contains('crashed'));

      final invalidResponseError = PaymentResult.invalidResponse('Malformed JSON');
      expect(invalidResponseError.message, contains('Invalid response'));
      expect(invalidResponseError.data?['details'], 'Malformed JSON');
    });
  });
}
