import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
 
/// PhonePe Payment Lifecycle Manager
/// Handles persistent payment state and app lifecycle for PhonePe payments
/// Based on official PhonePe documentation: polling for payment status when PENDING
class PhonePeLifecycleManager {
  static PhonePeLifecycleManager? _instance;
  static const String _pendingPaymentKey = 'phonepe_pending_payment';
  static const String _transactionDataKey = 'phonepe_transaction_data';
  static const String _paymentStartTimeKey = 'phonepe_payment_start_time';

  /// Get singleton instance
  static PhonePeLifecycleManager get instance {
    _instance ??= PhonePeLifecycleManager._();
    return _instance!;
  }

  PhonePeLifecycleManager._();

  /// Store pending PhonePe payment data
  Future<void> storePendingPayment({
    required String transactionId,
    required String orderId,
    required double amount,
    required Map<String, dynamic> paymentRequest,
  }) async {
    try {
      debugPrint('💾 PHONEPE LIFECYCLE: Storing pending payment data');
      debugPrint('💾 PHONEPE LIFECYCLE: Transaction ID: $transactionId');
      debugPrint('💾 PHONEPE LIFECYCLE: Order ID: $orderId');
      debugPrint('💾 PHONEPE LIFECYCLE: Amount: ₹$amount');

      final prefs = await SharedPreferences.getInstance();

      final paymentData = {
        'transactionId': transactionId,
        'orderId': orderId,
        'amount': amount,
        'paymentRequest': paymentRequest,
        'startTime': DateTime.now().toIso8601String(),
      };

      await prefs.setString(_pendingPaymentKey, 'true');
      await prefs.setString(_transactionDataKey, jsonEncode(paymentData));
      await prefs.setString(_paymentStartTimeKey, DateTime.now().toIso8601String());

      debugPrint('✅ PHONEPE LIFECYCLE: Pending payment data stored successfully');
    } catch (e) {
      debugPrint('❌ PHONEPE LIFECYCLE: Error storing pending payment: $e');
    }
  }

  /// Check if there's a pending PhonePe payment
  Future<bool> hasPendingPayment() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final hasPending = prefs.getBool(_pendingPaymentKey) ?? false;
      debugPrint('🔍 PHONEPE LIFECYCLE: Has pending payment: $hasPending');
      return hasPending;
    } catch (e) {
      debugPrint('❌ PHONEPE LIFECYCLE: Error checking pending payment: $e');
      return false;
    }
  }

  /// Get pending payment data
  Future<Map<String, dynamic>?> getPendingPaymentData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final dataString = prefs.getString(_transactionDataKey);
      
      if (dataString == null) {
        debugPrint('🔍 PHONEPE LIFECYCLE: No pending payment data found');
        return null;
      }

      final data = jsonDecode(dataString) as Map<String, dynamic>;
      debugPrint('🔍 PHONEPE LIFECYCLE: Retrieved pending payment data');
      debugPrint('🔍 PHONEPE LIFECYCLE: Transaction ID: ${data['transactionId']}');
      debugPrint('🔍 PHONEPE LIFECYCLE: Order ID: ${data['orderId']}');
      debugPrint('🔍 PHONEPE LIFECYCLE: Amount: ₹${data['amount']}');
      
      return data;
    } catch (e) {
      debugPrint('❌ PHONEPE LIFECYCLE: Error getting pending payment data: $e');
      return null;
    }
  }

  /// Clear pending payment data
  Future<void> clearPendingPayment() async {
    try {
      debugPrint('🧹 PHONEPE LIFECYCLE: Clearing pending payment data...');
      
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_pendingPaymentKey);
      await prefs.remove(_transactionDataKey);
      await prefs.remove(_paymentStartTimeKey);
      
      debugPrint('✅ PHONEPE LIFECYCLE: Pending payment data cleared');
    } catch (e) {
      debugPrint('❌ PHONEPE LIFECYCLE: Error clearing pending payment: $e');
    }
  }

  /// Handle app resume - check for pending PhonePe payments
  Future<PaymentResult?> handleAppResume() async {
    try {
      debugPrint('🔄 PHONEPE LIFECYCLE: ========== APP RESUME HANDLER ==========');

      final hasPending = await hasPendingPayment();
      if (!hasPending) {
        debugPrint('🔄 PHONEPE LIFECYCLE: No pending PhonePe payment found');
        return null;
      }

      final paymentData = await getPendingPaymentData();
      if (paymentData == null) {
        debugPrint('❌ PHONEPE LIFECYCLE: Pending payment data is null');
        await clearPendingPayment();
        return null;
      }

      debugPrint('🔍 PHONEPE LIFECYCLE: Found pending payment, checking status...');
      debugPrint('🔍 PHONEPE LIFECYCLE: Transaction ID: ${paymentData['transactionId']}');
      debugPrint('🔍 PHONEPE LIFECYCLE: Order ID: ${paymentData['orderId']}');

      // Check payment timeout (5 minutes as per PhonePe documentation)
      final startTimeString = paymentData['startTime'] as String?;
      if (startTimeString != null) {
        final startTime = DateTime.parse(startTimeString);
        final elapsed = DateTime.now().difference(startTime);
        
        if (elapsed.inMinutes > 5) {
          debugPrint('⏰ PHONEPE LIFECYCLE: Payment timed out (${elapsed.inMinutes} minutes)');
          await clearPendingPayment();
          return PaymentResult.timeout();
        }
      }

      // Try to get payment status from server
      final result = await _checkPaymentStatus(paymentData);

      if (result != null) {
        debugPrint('✅ PHONEPE LIFECYCLE: Payment status resolved: ${result.type}');
        await clearPendingPayment();
        return result;
      }

      debugPrint('⏳ PHONEPE LIFECYCLE: Payment status still pending');
      return null;

    } catch (e) {
      debugPrint('❌ PHONEPE LIFECYCLE: Error handling app resume: $e');
      await clearPendingPayment();
      return null;
    }
  }

  /// Check payment status from server (polling mechanism)
  Future<PaymentResult?> _checkPaymentStatus(Map<String, dynamic> paymentData) async {
    try {
      debugPrint('🔍 PHONEPE LIFECYCLE: Checking payment status from server...');
      
      final transactionId = paymentData['transactionId'] as String;
      final orderId = paymentData['orderId'] as String;
      
      // TODO: Implement server status check API call
      // This should call the Order Status API as mentioned in PhonePe documentation
      // For now, return null to indicate status is still pending
      
      debugPrint('🔍 PHONEPE LIFECYCLE: Server status check not implemented yet');
      return null;
      
    } catch (e) {
      debugPrint('❌ PHONEPE LIFECYCLE: Error checking payment status: $e');
      return null;
    }
  }

  /// Get payment start time
  Future<DateTime?> getPaymentStartTime() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final timeString = prefs.getString(_paymentStartTimeKey);
      
      if (timeString == null) return null;
      
      return DateTime.parse(timeString);
    } catch (e) {
      debugPrint('❌ PHONEPE LIFECYCLE: Error getting payment start time: $e');
      return null;
    }
  }

  /// Check if payment has timed out
  Future<bool> hasPaymentTimedOut({Duration timeout = const Duration(minutes: 5)}) async {
    try {
      final startTime = await getPaymentStartTime();
      if (startTime == null) return false;
      
      final elapsed = DateTime.now().difference(startTime);
      return elapsed > timeout;
    } catch (e) {
      debugPrint('❌ PHONEPE LIFECYCLE: Error checking timeout: $e');
      return false;
    }
  }
}
